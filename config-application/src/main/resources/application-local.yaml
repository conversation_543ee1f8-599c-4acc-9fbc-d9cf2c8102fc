server:
  port: 10001
  servlet:
    context-path: /api/config
  compression:
    enabled: true
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: always
    include-exception: true
    include-client-error-message: true
    include-server-error-message: true
    send-client-error-email: false
    send-server-error-email: false
  forward-headers-strategy: framework

spring:
  application:
    name: ConfigService
  threads:
    virtual:
      enabled: false

springdoc:
  show-actuator: true
  swagger-ui:
    filter: true

keycloak:
  host: ${KEYCLOAK_URL:http://localhost:8080}
  public-host: ${KEYCLOAK_PUBLIC_URL:http://localhost:3030}
  admin:
    url: ${keycloak.host}
    realm: master
    client-id: ${KEYCLOAK_ADMIN_CLIENT_ID}
    client-secret: ${KEYCLOAK_ADMIN_CLIENT_SECRET}
  swagger:
    url: ${keycloak.host}
    realm: clerkXpress
    client-id: ${KEYCLOAK_SWAGGER_AUTH_CLIENT_ID}

com.c4-soft.springaddons.oidc:
  ops:
    - iss: ${keycloak.public-host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
    - iss: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/actuator/**"
      - "/public/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - http://localhost:3000
          - https://localhost:3000
          - ${keycloak.public-host}

encryption:
  secretKey: ${ENCRYPTION_SECRET_KEY}

multi-tenancy:
  enabled: true
  app-properties:
    enabled: false
  async:
    enabled: true
  caching:
    enabled: true
  keycloak:
    enabled: true
  management:
    enabled: true
  rabbitmq:
    enabled: true
    host: ${SPRING_RABBITMQ_HOST:localhost}
    port: 5672
  database:
    enabled: true
    type: DATABASE_PER_TENANT
    default-tenant: postgres
    tenancy-format: "%s"
    liquibase:
      change-log: "classpath:/db/changelog/db.changelog-master.xml"
      tenant-migration:
        chazy:
          change-log: "classpath:/db/changelog/tenant/chazy-changelog-master.xml"
    datasource:
      url: "jdbc:postgresql://${DATABASE_HOST:localhost}:5432/postgres?currentSchema=config"
      username: ${SPRING_DATASOURCE_USERNAME}
      password: ${SPRING_DATASOURCE_PASSWORD}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximumPoolSize: 5
        minimumIdle: 5
        idleTimeout: 30000
        maxLifetime: 60000
        connectionTimeout: 30000

logging:
  pattern:
    level: "%5p [tenantId=%X{tenantId:-}], [%X{traceId:-},%X{spanId:-}] [user=%X{userEmail:-}]"
  level:
    org.springframework.web: "info"
    com.scube: "debug"

com.scube.client:
  auth: "http://${CLIENT_LIB_HOST:localhost}:9001/api/auth"
