server:
  port: 10001
  servlet:
    context-path: /api/config
  compression:
    enabled: true
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: never
    include-exception: false
    include-client-error-message: true
    include-server-error-message: false
    send-client-error-email: true
    send-server-error-email: true
  forward-headers-strategy: framework

spring:
  application:
    name: ConfigService
  threads:
    virtual:
      enabled: false

springdoc:
  show-actuator: true
  swagger-ui:
    enabled: false
    filter: true

keycloak:
  host: http://keycloak.keycloak.svc.cluster.local:8080
  public-host: https://auth-staging.clerkxpress.com
  admin:
    url: ${keycloak.host}
    realm: master
    client-id: ${KEYCLOAK_ADMIN_CLIENT_ID}
    client-secret: ${KEYCLOAK_ADMIN_CLIENT_SECRET}
  swagger:
    url: ${keycloak.public-host}
    realm: clerkXpress
    client-id: ${KEYCLOAK_SWAGGER_AUTH_CLIENT_ID}

com.c4-soft.springaddons.oidc:
  ops:
    - iss: ${keycloak.host}
      jwkSetUri: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
    - iss: ${keycloak.public-host}
      jwk-set-uri: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/actuator/**"
      - "/public/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - https://staging.clerkxpress.com

encryption:
  secretKey: ${ENCRYPTION_SECRET_KEY}

multi-tenancy:
  enabled: true
  app-properties:
    enabled: false
  async:
    enabled: true
  caching:
    enabled: true
  keycloak:
    enabled: true
  management:
    enabled: true
  rabbitmq:
    enabled: true
    host: "rabbitmq.backend.svc.cluster.local"
    port: 5672
  database:
    enabled: true
    type: DATABASE_PER_TENANT
    default-tenant: postgres
    tenancy-format: "%s"
    liquibase:
      change-log: "classpath:/db/changelog/db.changelog-master.xml"
    datasource:
      url: "******************************************************************"
      username: ${SPRING_DATASOURCE_USERNAME}
      password: ${SPRING_DATASOURCE_PASSWORD}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximumPoolSize: 5
        minimumIdle: 5
        idleTimeout: 30000
        maxLifetime: 60000
        connectionTimeout: 30000

logging:
  pattern:
    level: "%5p [tenantId=%X{tenantId:-}], [%X{traceId:-},%X{spanId:-}] [user=%X{userEmail:-}]"
  level:
    org.springframework.web: "info"

com.scube.client:
  auth: "http://scube-auth-service-srv:9001/api/auth"
