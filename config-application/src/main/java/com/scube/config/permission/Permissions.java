package com.scube.config.permission;

/**
 * Generated DO NOT MODIFY!
 * date: 2025-06-05T13:39:03.011831700Z
 */
public class Permissions {
    private Permissions() {
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.012836500Z
     */
    public static class LoggedInUserJsonStorage {
        public static final String GET_JSON_STORAGE = "config-service-me-json-storage-get-json-storage";

        private LoggedInUserJsonStorage() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.016836600Z
     */
    public static class Tenant {
        public static final String CREATE_TENANT = "config-service-tenant-create-tenant";

        public static final String JOIN_TENANT = "config-service-tenant-join-tenant";

        public static final String LEAVE_TENANT = "config-service-tenant-leave-tenant";

        public static final String MAKE_ACTIVE_TENANT = "config-service-tenant-make-active-tenant";

        public static final String GET_ACTIVE_TENANT = "config-service-tenant-get-active-tenant";

        public static final String GET_USER_TENANTS = "config-service-tenant-get-user-tenants";

        public static final String GET_USER_ROLES = "config-service-tenant-get-user-roles";

        private Tenant() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.017838600Z
     */
    public static class Cache {
        public static final String EVICT_CACHE = "config-service-cache-evict-cache";

        public static final String EVICT_APP_PROPERTY_CACHE = "config-service-cache-evict-app-property-cache";

        public static final String EVICT_JSON_STORAGE_CACHE = "config-service-cache-evict-json-storage-cache";

        public static final String EVICT_SQL_STORAGE_CACHE = "config-service-cache-evict-sql-storage-cache";

        private Cache() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.017838600Z
     */
    public static class TenantMigration {
        public static final String MIGRATE_ALL_TENANTS = "config-service-tenant-migration-migrate-all-tenants";

        public static final String MIGRATE_TENANT = "config-service-tenant-migration-migrate-tenant";

        private TenantMigration() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.017838600Z
     */
    public static class AppProperty {
        public static final String GET_PROPERTIES = "config-service-app-property-get-properties";

        public static final String GET_PROPERTY = "config-service-app-property-get-property";

        public static final String SET_PROPERTIES = "config-service-app-property-set-properties";

        public static final String DELETE_PROPERTY = "config-service-app-property-delete-property";

        private AppProperty() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.017838600Z
     */
    public static class LoggedInUserTenant {
        public static final String JOIN_TENANT = "config-service-me-tenant-join-tenant";

        public static final String LEAVE_TENANT = "config-service-me-tenant-leave-tenant";

        public static final String MAKE_ACTIVE_TENANT = "config-service-me-tenant-make-active-tenant";

        public static final String GET_ACTIVE_TENANT = "config-service-me-tenant-get-active-tenant";

        public static final String REMOVE_ACTIVE_TENANT = "config-service-me-tenant-remove-active-tenant";

        public static final String GET_TENANTS = "config-service-me-tenant-get-tenants";

        public static final String GET_ROLES = "config-service-me-tenant-get-roles";

        private LoggedInUserTenant() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.017838600Z
     */
    public static class Permission {
        public static final String SEED_ROLES_TO_ALL_REALMS = "config-service-permissions-seed-roles-to-all-realms";

        public static final String SEED_ROLES_BY_REALM = "config-service-permissions-seed-roles-by-realm";

        private Permission() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.018841500Z
     */
    public static class LoggedInUserRuleEngine {
        public static final String EVALUATE = "config-service-me-rule-evaluate";

        private LoggedInUserRuleEngine() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.018841500Z
     */
    public static class PublicTenant {
        public static final String GET_TENANTS = "config-service-public-tenant-get-tenants";

        public static final String GET_IDENTITY_PROVIDERS = "config-service-public-tenant-get-identity-providers";

        private PublicTenant() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.018841500Z
     */
    public static class JsonStorage {
        public static final String GET_JSON_STORAGE = "config-service-json-storage-get-json-storage";

        public static final String GET_JSON_STORAGE_HISTORY = "config-service-json-storage-get-json-storage-history";

        public static final String CREATE_UPDATE_JSON_STORAGE = "config-service-json-storage-create-update-json-storage";

        public static final String DELETE_JSON_STORAGE = "config-service-json-storage-delete-json-storage";

        private JsonStorage() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.018841500Z
     */
    public static class RuleEngine {
        public static final String EVALUATE = "config-service-rule-evaluate";

        private RuleEngine() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-06-05T13:39:03.018841500Z
     */
    public static class SqlStorage {
        public static final String GET_ALL = "config-service-sql-storage-get-all";

        public static final String GET = "config-service-sql-storage-get";

        public static final String GET_BY_UUID = "config-service-sql-storage-get-by-uuid";

        public static final String GET_BY_NAME = "config-service-sql-storage-get-by-name";

        public static final String GET_NAMES = "config-service-sql-storage-get-names";

        public static final String GET_ALL_5 = "config-service-sql-storage-get-all";

        public static final String CREATE_OR_UPDATE = "config-service-sql-storage-create-or-update";

        public static final String CREATE_OR_UPDATE_1 = "config-service-sql-storage-create-or-update";

        public static final String CREATE_OR_UPDATE_2 = "config-service-sql-storage-create-or-update";

        public static final String DELETE_BY_ID = "config-service-sql-storage-delete-by-id";

        public static final String DELETE_BY_UUID = "config-service-sql-storage-delete-by-uuid";

        public static final String DELETE_BY_NAME = "config-service-sql-storage-delete-by-name";

        private SqlStorage() {
        }
    }
}
