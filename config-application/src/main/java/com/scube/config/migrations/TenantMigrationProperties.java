package com.scube.config.migrations;

import lombok.Data;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.Nullable;

import java.util.Map;
import java.util.Optional;

@Data
@Configuration
@ConfigurationProperties("multi-tenancy.database.liquibase")
public class TenantMigrationProperties {
    private Map<String, LiquibaseProperties> tenantMigration;

    @Nullable
    public LiquibaseProperties getTenantMigrationProperties(String tenantId) {
        return Optional.ofNullable(tenantMigration)
                .map(m -> m.get(tenantId))
                .orElse(null);
    }
}