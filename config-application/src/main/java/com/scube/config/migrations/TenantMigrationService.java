package com.scube.config.migrations;

import com.scube.auth.library.ITokenService;
import com.scube.multi.tenant.TenantContext;
import com.scube.multi.tenant.tenancy.database.hibernate.db_per_tenant.DPTRoutingDatasource;
import com.scube.multi.tenant.tenancy.tenant_runner.LoopPerTenant;
import liquibase.exception.LiquibaseException;
import liquibase.integration.spring.SpringLiquibase;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;

import static com.scube.multi.tenant.tenancy.database.migration.liquibase.LiquibaseUtils.getSpringLiquibase;

@Slf4j
@Service
@RequiredArgsConstructor
@Profile("!test") // Exclude from test profile
public class TenantMigrationService {
    private final DPTRoutingDatasource dptRoutingDatasource;
    private final TenantMigrationProperties properties;
    private final ITokenService tokenService;

    public void migrate(String tenantId) {
        // get an admin token for the tenant
        tokenService.getNewTokenAndAuthenticate(tenantId);
        try {
            log.debug("Starting tenant migration for tenant: {}", tenantId);
            var liquibaseProperties = properties.getTenantMigrationProperties(tenantId);
            if (liquibaseProperties == null) {
                log.warn("No Liquibase properties found for tenant: {}", tenantId);
                return;
            }
            DataSource dataSource = dptRoutingDatasource.createIfNotExists(tenantId);
            SpringLiquibase liquibase = getSpringLiquibase(liquibaseProperties, dataSource, null);
            liquibase.afterPropertiesSet();
            log.debug("Tenant migration completed for tenant: {}", tenantId);
        } catch (LiquibaseException e) {
            throw new RuntimeException("Tenant migration failed for tenant: %s".formatted(tenantId), e);
        }
    }

    @LoopPerTenant
    public void migrateAllTenants() {
        var tenantId = TenantContext.getTenantId();
        this.migrate(tenantId);
    }
}
