package com.scube.config.migrations.custom_change;

import com.scube.lib.misc.BeanUtils;
import com.scube.multi.tenant.management.create.CreateTenantCommand;
import com.scube.rabbit.core.AmqpGateway;
import liquibase.change.custom.CustomTaskChange;
import liquibase.database.Database;
import liquibase.exception.CustomChangeException;
import liquibase.exception.SetupException;
import liquibase.exception.ValidationErrors;
import liquibase.resource.ResourceAccessor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class CreateTenantChange implements CustomTaskChange {
    private String tenantId;

    @Override
    public void execute(Database database) throws CustomChangeException {
        AmqpGateway gateway = BeanUtils.getBean(AmqpGateway.class);
        gateway.publish(new CreateTenantCommand(tenantId));
    }

    @Override
    public String getConfirmationMessage() {
        return "Tenant with ID " + tenantId + " has been created successfully.";
    }

    @Override
    public void setUp() throws SetupException {
        // No setup required for this change
    }

    @Override
    public void setFileOpener(ResourceAccessor resourceAccessor) {
        // No file operations needed for this change
    }

    @Override
    public ValidationErrors validate(Database database) {
        ValidationErrors errors = new ValidationErrors();
        if (tenantId == null || tenantId.isBlank()) {
            errors.addError("tenantId must be provided");
        }
        return errors;
    }
}
