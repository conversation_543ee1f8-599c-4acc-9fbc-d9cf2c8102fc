package com.scube.config.migrations;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.config.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/tenant-migration")
@Validated
@GenerateHttpExchange(ServiceUrlConstant.CONFIG_SERVICE)
@RequiredArgsConstructor
@Profile("!test") // Exclude from test profile
public class TenantMigrationController {
    private final TenantMigrationService service;

    @PostMapping("/all")
    @RolesAllowed(Permissions.TenantMigration.MIGRATE_ALL_TENANTS)
    public void migrateAllTenants() {
        service.migrateAllTenants();
    }

    @PostMapping("{tenantId}")
    @RolesAllowed(Permissions.TenantMigration.MIGRATE_TENANT)
    public void migrateTenant(@PathVariable @Size(max = 40) String tenantId) {
        service.migrate(tenantId);
    }
}