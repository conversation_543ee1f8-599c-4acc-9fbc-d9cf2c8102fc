package com.scube.config.migrations;

import com.scube.multi.tenant.tenancy.tenant_runner.ExcludeTenantCreation;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@ExcludeTenantCreation
@Profile("!test") // Exclude from test profile
public class RunAllTenantMigrationRunner implements CommandLineRunner {
    private final TenantMigrationService service;

    @Override
    public void run(String... args) throws Exception {
        service.migrateAllTenants();
    }
}
