package com.scube.payment.features.providers.stripe;

import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItemFee;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.config_utils.app_property.AppPropertyValue;
import com.scube.payment.features.payment.enums.PaymentStatus;
import com.scube.payment.features.payment.enums.RefundStatus;
import com.scube.payment.features.payment.processing.dto.PayeeDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentResponseDto;
import com.scube.payment.features.payment.processing.rabbit.RefundedEvent;
import com.scube.payment.features.payment.processing.service.PaymentProcessingService;
import com.scube.payment.features.payment.receipts.service.ReceiptService;
import com.scube.payment.features.payment.storage.model.Payment;
import com.scube.payment.features.payment.storage.model.RefundTransaction;
import com.scube.payment.features.payment.storage.service.PaymentStorageService;
import com.scube.payment.features.providers.gateway.IPaymentProviderGateway;
import com.scube.payment.features.providers.gateway.PaymentTokenRequest;
import com.scube.payment.features.providers.gateway.PaymentTokenResponse;
import com.scube.payment.features.webhook.inbox.domain_events.PaymentDetailsEvent;
import com.scube.rabbit.core.AmqpGateway;
import com.stripe.Stripe;
import com.stripe.model.*;
import com.stripe.model.checkout.Session;
import com.stripe.param.RefundCreateParams;
import com.stripe.param.checkout.SessionCreateParams;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.scube.payment.features.payment.util.PaymentUtils.convertToCents;
import static com.scube.payment.features.providers.gateway.PaymentProviderGateway.GATEWAY;
import static com.scube.payment.features.providers.stripe.StripeProperties.PAYMENT_PROVIDER_NAME;
import static org.apache.commons.lang3.StringUtils.defaultString;

@Service(StripeGateway.NAME)
@RequiredArgsConstructor
@Slf4j
@Profile("!test")
public class StripeGateway implements IPaymentProviderGateway<PaymentTokenRequest, PaymentTokenResponse, StripeEvent> {
    public static final String NAME = PAYMENT_PROVIDER_NAME + GATEWAY;
    public static final String APP_FEE_PREFIX = "APP_FEE_";
    public static final int BIGDECIMAL_TO_LONG_MULTIPLIER = 100;
    private final PaymentProcessingService paymentProcessingService;
    private final CalculationServiceConnection calculationServiceConnection;
    private final ReceiptService receiptService;
    private final PaymentStorageService paymentStorageService;
    private final AmqpGateway amqpGateway;

    @AppPropertyValue
    private StripeProperties stripeProperties;

    @Override
    @Transactional
    @SneakyThrows
    public PaymentTokenResponse getToken(PaymentTokenRequest paymentTokenRequest) {
        log.debug("Getting payment token for order: {}", paymentTokenRequest.getOrderId());

        Stripe.apiKey = stripeProperties.getSecretKey();

        String orderId = paymentTokenRequest.getOrderId().toString();

        SessionCreateParams.Builder paramsBuilder = SessionCreateParams.builder()
                .setSuccessUrl(stripeProperties.getSuccessUrl().replace("{orderId}", orderId))
                .setCancelUrl(stripeProperties.getCancelUrl())
                .setMode(SessionCreateParams.Mode.PAYMENT)
                .putMetadata("order_id", orderId)
                .addAllLineItem(toLineItems(paymentTokenRequest.getItems()))
                .setCurrency("usd");
        if (stripeProperties.useConnectedAccount()) {
            Long applicationFeeAmount = getApplicationFeeAmount(paymentTokenRequest.getOrder());
            paramsBuilder.setPaymentIntentData(
                    SessionCreateParams.PaymentIntentData.builder()
                            .setApplicationFeeAmount(applicationFeeAmount)
                            .setTransferData(
                                    SessionCreateParams.PaymentIntentData.TransferData.builder()
                                            .setDestination(stripeProperties.getConnectedAccountId())
                                            .build()
                            )
                            .build()
            );
        }

        Session session = Session.create(paramsBuilder.build());

        return new PaymentTokenResponse(session.getId(), orderId, PAYMENT_PROVIDER_NAME, Map.of("key", stripeProperties.getPublishableKey()));
    }

    @Override
    @SneakyThrows
    public void authCapture(StripeEvent stripeEvent) {
        log.debug("Processing authCapture webhook: {}", stripeEvent);

        Stripe.apiKey = stripeProperties.getSecretKey();
        Event event = stripeEvent.deserialize();
        if (!event.getApiVersion().equalsIgnoreCase(Stripe.API_VERSION)) {
            log.error("Stripe API version mismatch. Expected: {}, Actual: {}", Stripe.API_VERSION, event.getApiVersion());
            log.info("updating Stripe API version to: {}", Stripe.API_VERSION);
            event.setApiVersion(Stripe.API_VERSION);
        }

        Session session = getSession(event);

        PaymentIntent paymentIntent = PaymentIntent.retrieve(session.getPaymentIntent());

        // If there is an existing paymentReference, it means one of the other payment service replicas
        // has already processed this authcapture.
        if (paymentStorageService.existsByPaymentReference(session.getPaymentIntent())) {
            return;
        }

        Session.CustomerDetails customerDetails = session.getCustomerDetails();

        PayeeDto payeeDto = PayeeDto.builder()
                .email(defaultString(customerDetails.getEmail()))
                .phone(defaultString(customerDetails.getPhone()))
                .build();

        payeeDto.addName(customerDetails.getName());

        var address = customerDetails.getAddress();
        if (address != null) {
            payeeDto.setMailingAddress(address.getLine1());
            payeeDto.setMailingAddress2(address.getLine2());
            payeeDto.setMailingCity(address.getCity());
            payeeDto.setMailingState(address.getState());
            payeeDto.setMailingZipCode(address.getPostalCode());
            payeeDto.setBillingSameAsMailing(true);
        }

        UUID orderId = UUID.fromString(session.getMetadata().get("order_id"));

        OrderInvoiceResponse orderInvoiceResponse = calculationServiceConnection.order().getOrder(orderId);

        SubmitPaymentRequestDto request = SubmitPaymentRequestDto
                .builder()
                .orderId(orderId)
                .orderAmount(orderInvoiceResponse.getTotal())
                .payee(payeeDto)
                .paymentType(getPaymentType(paymentIntent.getPaymentMethodTypes().getFirst()))
                .paymentAmount(BigDecimal.valueOf(paymentIntent.getAmountReceived()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                .paymentReference(paymentIntent.getId())
                .status(PaymentStatus.COMPLETED)
                .transactionDate(Instant.ofEpochSecond(paymentIntent.getCreated()))
                .authorizedTs(Instant.ofEpochSecond(paymentIntent.getCreated()))
                .capturedTs(Instant.ofEpochSecond(paymentIntent.getCreated()))
                .paymentProvider(PAYMENT_PROVIDER_NAME)
                .isOnlineTransaction(true)
                .build();

        SubmitPaymentResponseDto paymentResp = paymentProcessingService.submitPayment(request);

        receiptService.generateReceipt(request, paymentResp.getPaymentId(), orderInvoiceResponse);
    }

    @Override
    public void settle(StripeEvent event) {
        ;
    }

    @Override
    @SneakyThrows
    public void createRefund(RefundTransaction refundTransaction) {
        Stripe.apiKey = stripeProperties.getSecretKey();

        String paymentIntentId = refundTransaction.getPayment().getPaymentReference();
        Long amountInCents = refundTransaction.getAmountInCents();

        try {
            Map<String, Object> chargeParams = new HashMap<>();
            chargeParams.put("payment_intent", paymentIntentId);

            ChargeCollection chargeCollection = Charge.list(chargeParams);
            List<Charge> charges = chargeCollection.getData();

            if (charges.isEmpty()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No charges found for PaymentIntent ID: " + paymentIntentId);
            }

            String chargeId = charges.getFirst().getId();

            RefundCreateParams.Builder refundParams = RefundCreateParams.builder()
                    .setCharge(chargeId);

            if (amountInCents != null) {
                refundParams.setAmount(amountInCents);
            }

            Refund refund = Refund.create(refundParams.build());
            log.info("Refund created. Refund ID: {}, Status: {}", refund.getId(), refund.getStatus());

            paymentStorageService.updateRefund(refundTransaction, refund.getId());

        } catch (Exception e) {
            log.error("Refund failed for PaymentIntent ID: " + paymentIntentId, e);
            paymentStorageService.updateRefundStatus(refundTransaction, RefundStatus.REJECTED);
        }
    }

    @Override
    public void refundFailed(StripeEvent event) {
        Stripe.apiKey = stripeProperties.getSecretKey();

        Event stripeRawEvent = event.deserialize();
        Refund refund = (Refund) stripeRawEvent.getData().getObject();
        String refundId = refund.getId();

        RefundTransaction refundTransaction = paymentStorageService.getByRefundReference(refundId);
        if (refundTransaction == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "RefundTransaction not found for refundId: " + refundId);
        }

        paymentStorageService.updateRefundStatus(refundTransaction, RefundStatus.FAILED);
    }

    @Override
    public void refund(StripeEvent event) {
        Stripe.apiKey = stripeProperties.getSecretKey();

        Event stripeRawEvent = event.deserialize();
        Refund refund = (Refund) stripeRawEvent.getData().getObject();
        String refundId = refund.getId();

        RefundTransaction refundTransaction = paymentStorageService.getByRefundReference(refundId);
        if (refundTransaction == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "RefundTransaction not found for refundId: " + refundId);
        }

        paymentStorageService.updateRefundStatus(refundTransaction, RefundStatus.COMPLETED);

        OrderInvoiceResponse order = calculationServiceConnection.order().getRefundableOrder(refundTransaction.getOrderId());
        BigDecimal refundedAmount = paymentStorageService.getRefundedAmount(refundTransaction.getOrderId());

        amqpGateway.publish(new RefundedEvent(order.getOrderId(), refundedAmount));
    }


    @Override
    public void _void(StripeEvent event) {
        Stripe.apiKey = stripeProperties.getSecretKey();

        paymentStorageService._void(getPayment(event.deserialize()));
    }

    private @NotNull Payment getPayment(Event event) {
        Charge charge = (Charge) event.getDataObjectDeserializer().getObject().orElseThrow(
                () -> new RuntimeException("Failed to retrieve Stripe payment for event: " + event.getId()));

        return paymentStorageService.getByPaymentReference(charge.getPaymentIntentObject().getId());
    }

    private static String getPaymentType(String paymentType) {
        return switch (paymentType) {
            case "card" -> "card";
            case "ach_credit_transfer", "bank_account" -> "ach";
            default -> paymentType;
        };
    }

    public static List<SessionCreateParams.LineItem> toLineItems(@NotNull List<OrderInvoiceItem> items) {
        return items.stream().map(StripeGateway::toLineItem).toList();
    }

    private static SessionCreateParams.LineItem toLineItem(@NotNull OrderInvoiceItem item) {
        return SessionCreateParams.LineItem.builder()
                .setQuantity(1L)
                .setPriceData(
                        SessionCreateParams.LineItem.PriceData.builder()
                                .setCurrency("usd")
                                .setUnitAmount(convertToCents(item.getTotal()))
                                .setTaxBehavior(SessionCreateParams.LineItem.PriceData.TaxBehavior.INCLUSIVE)
                                .setProductData(
                                        SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                                .setName(item.getPrimaryDisplay())
                                                .setDescription(item.getSecondaryDisplay())
                                                .build()
                                ).build()
                )
                .build();
    }

    private Long getApplicationFeeAmount(@NotNull OrderInvoiceResponse order) {
        BigDecimal orderItemFee = order.getItems().stream()
                .flatMap(x -> x.getFees().stream())
                // get any that starts with fee code of "APP_FEE_"
                .filter(fee -> fee.getFeeCode().startsWith(APP_FEE_PREFIX))
                .map(OrderInvoiceItemFee::getAmount)
                .map(fee -> fee.multiply(BigDecimal.valueOf(BIGDECIMAL_TO_LONG_MULTIPLIER)))
                .map(fee -> fee.setScale(0, RoundingMode.HALF_UP))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal additionalFee = Optional.ofNullable(order.getOrderAdditionalFees()).orElse(List.of()).stream()
//                .flatMap(x -> Optional.ofNullable(x.getFees()).orElse(List.of()).stream())
//                .map(AdditionalItemFeeDto::getFee)
//                .filter(Objects::nonNull)
//                .filter(fee -> fee.getKey().startsWith(APP_FEE_PREFIX))
//                .map(FeeDto::getAmount)
//                .map(fee -> fee.multiply(BigDecimal.valueOf(BIGDECIMAL_TO_LONG_MULTIPLIER)))
//                .map(fee -> fee.setScale(0, RoundingMode.HALF_UP))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return orderItemFee.longValue();
    }

    @SneakyThrows
    private static Session getSession(@NotNull Event event) {
        EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
        if (dataObjectDeserializer.getObject().isPresent()) {
            return (Session) dataObjectDeserializer.getObject().get();
        }

        log.error("Failed to deserialize stripeEvent: {}", event.getId());
        throw new RuntimeException("Failed to retrieve Stripe payment orderId");
    }

    public PaymentDetailsEvent getPaymentDetails(StripeEvent event) {
        Stripe.apiKey = stripeProperties.getSecretKey();
        Event stripeRawEvent = event.deserialize();
        String eventType = stripeRawEvent.getType();
        if (eventType.startsWith("refund.")) {
            Session session = getSession(stripeRawEvent);
            try {
                Refund refund = (Refund) stripeRawEvent.getData().getObject();
                String refundId = refund.getId();

                RefundTransaction refundTx = paymentStorageService.getByRefundReference(refundId);
                if (refundTx == null) {
                    throw new ResponseStatusException(HttpStatus.NOT_FOUND, "RefundTransaction not found for refundId: " + refundId);
                }
                return PaymentDetailsEvent.builder()
                        .orderId(refundTx.getOrderId())
                        .build();
            } catch (Exception ex) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Expected Refund object for refund event");
            }
        } else {
            Session session = getSession(stripeRawEvent);
            String orderId = session.getMetadata().get("order_id");

            if (orderId == null) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Order ID not found in session metadata");
            }

            return PaymentDetailsEvent.builder()
                    .orderId(UUID.fromString(orderId))
                    .build();
        }
    }
}
