update config.sql_storage
set sql_query = $$
SELECT 
	o.order_id,
    TO_CHAR(o.order_paid_date, 'MM/DD/YYYY') AS "orderPaidDate",
    l.license_number,    
    CONCAT(
        participant.properties->>'firstName', 
        ' ', 
        participant.properties->>'lastName'
    ) AS ownerName, 
    TO_CHAR(l.valid_to_date, 'MM/DD/YYYY') AS "expirationDate",
    fees.is_exempt AS "exempt",
    fees.has_senior_discount AS "seniorDiscount",
	fees.local_fees::NUMERIC(20,2)::TEXT AS "localFees",
	fees.state_fees::NUMERIC(20,2)::TEXT AS "stateFees",
	fees.stripe_fees::NUMERIC(20,2)::TEXT AS "stripeFees",
	fees.total_fees::NUMERIC(20,2)::TEXT AS "totalFees",
    fees.is_altered AS altered,
    CASE WHEN oi.name LIKE 'Purebred%' THEN
		CASE WHEN activities.activity_count > 0 THEN
			CASE
				WHEN activities.activity_type = 'NEW' THEN 'Purebred '
				ELSE 'Purebred '
			END 
		ELSE
			'Purebred '
		END
	ELSE
		CASE WHEN activities.activity_count > 0 THEN
			CASE
				WHEN activities.activity_type = 'NEW' THEN 'Original '
				ELSE 'Renewal '
			END 
		ELSE
			'Original '
		END
	END
    || CASE WHEN fees.is_altered THEN 'Altered ' ELSE 'Unaltered ' END
    AS "feeName",
	activities.activity_count || ' years' AS "years"
FROM calculation.order_ o
INNER JOIN calculation.order_item oi 
    ON o.order_id = oi.order_id
    AND o.status = 'ORDER_PAID'
    AND DATE(o.order_paid_date) >= DATE(:startDate)
    AND DATE(o.order_paid_date) <= DATE(:endDate)
    AND (oi.name LIKE 'Dog License%' OR oi.name LIKE 'Purebred%')
    AND oi.item_type_id = 'license'
INNER JOIN license.license l 
    ON l.license_uuid = oi.unique_item_id
INNER JOIN payment.payment p 
    ON p.order_id = o.order_id
	AND p.is_online_transaction = true
INNER JOIN license.association assoc
    ON assoc.parent_association_type = 'LICENSE'
    AND assoc.child_association_type = 'PARTICIPANT'
    AND assoc.parent_id = l.license_id
INNER JOIN license.view_participant vp
    ON vp.participant_id = assoc.child_id
    AND vp.group_name = 'Individual'
INNER JOIN license.participant participant
    ON participant.participant_id = vp.participant_id
LEFT JOIN LATERAL calculation.get_dog_license_fees_by_order_item_id(oi.order_item_id) AS fees
    ON true
LEFT JOIN LATERAL (
    SELECT
        COUNT(*) AS activity_count,
        MIN(CASE 
		WHEN la.activity_type = 'NEW' THEN 'NEW'
		ELSE 'RENEWAL' END) AS activity_type,
		la.license_id
    FROM license.license_activity la
    inner join license.license_activity_fee laf
		on laf.order_id = o.order_id
		and laf.license_activity_id = la.license_activity_id
	group by la.license_id
) AS activities ON l.license_id = activities.license_id
ORDER BY o.order_paid_date;
$$
where name = 'DogTransactionReportOnlineTransactions';