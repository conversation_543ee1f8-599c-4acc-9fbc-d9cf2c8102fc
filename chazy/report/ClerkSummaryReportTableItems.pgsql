update config.sql_storage
set sql_query = $$
WITH possible_titles AS (
    SELECT 'New, Altered' AS title, 0 as sort_order, 21 as cost
    UNION ALL SELECT 'New, Unaltered', 1, 28
    UNION ALL SELECT 'Renewal, Altered', 2, 21
    UNION ALL SELECT 'Renewal, Unaltered', 3, 28
    UNION ALL SELECT 'License Exempt', 4, 0
    UNION ALL SELECT 'Dog Tag', 5, 5
),
license_activity_fees AS (
    SELECT DISTINCT
        laf.license_activity_id AS entity_id,
        CASE 
            WHEN laf.has_exempt_fee THEN 'License Exempt'
            WHEN laf.is_new AND laf.has_altered_fee THEN 'New, Altered'
            WHEN laf.is_new AND laf.has_unaltered_fee THEN 'New, Unaltered'
            WHEN laf.is_renewal AND laf.has_altered_fee THEN 'Renewal, Altered'
            WHEN laf.is_renewal AND laf.has_unaltered_fee THEN 'Renewal, Unaltered'
            ELSE 'Unknown'
        END AS title,
        laf.total
    FROM calculation.order_ o
    INNER JOIN calculation.order_item oi 
        ON o.order_id = oi.order_id
    INNER JOIN license.view_license_activity_fee_by_order laf
        ON laf.order_id = o.order_id
    WHERE o.status ILIKE 'ORDER_PAID'
        AND oi.name ILIKE 'Dog License%'
        AND oi.item_type_id ILIKE 'license'
        AND o.order_paid_date::DATE BETWEEN :startDate::date AND :endDate::date
),
dog_tags AS (
    SELECT
        oi.order_item_id AS entity_id,
        'Dog Tag' AS title,
        f.amount AS total
    FROM calculation.order_ o
    INNER JOIN calculation.order_item oi 
        ON o.order_id = oi.order_id
    INNER JOIN calculation.order_item_fee laf
        ON laf.order_item_id = oi.order_item_id
    INNER JOIN calculation.fee f
        ON f.fee_id = laf.fee_id
    WHERE o.status ILIKE 'ORDER_PAID'
        AND oi.name ILIKE 'Dog Tag%'
        AND oi.item_type_id ILIKE 'tag'
        AND o.order_paid_date::DATE BETWEEN :startDate::date AND :endDate::date
),
all_fees AS (
    SELECT title, total 
    FROM license_activity_fees
    UNION ALL
    SELECT title, total 
    FROM dog_tags
),
fees_by_amount AS (
    SELECT
        title,
        total AS amount,
        COUNT(*) AS quantity,
        total * COUNT(*) AS total_amount
    FROM all_fees
    GROUP BY title, total
)
SELECT 
    pt.title,
    COALESCE(TO_CHAR(fba.amount, '$FM999,999,990.00'), '$0.00') AS amount,
    COALESCE(fba.quantity, 0) AS quantity, 
    COALESCE(TO_CHAR(fba.total_amount, '$FM999,999,990.00'), '$0.00') AS total,
    false AS is_total
FROM possible_titles pt
LEFT JOIN fees_by_amount fba ON pt.title = fba.title
WHERE fba.quantity IS NOT NULL

UNION ALL

SELECT 
    pt.title,
    TO_CHAR(pt.cost, '$FM999,999,990.00') AS amount,
    0 AS quantity, 
    '$0.00' AS total,
    false AS is_total
FROM possible_titles pt
WHERE NOT EXISTS (
    SELECT 1 FROM fees_by_amount fba WHERE pt.title = fba.title
)

UNION ALL

SELECT 
    'Total' AS title,
    '' AS amount,
    COALESCE(SUM(fba.quantity), 0) AS quantity,
    TO_CHAR(COALESCE(SUM(fba.total_amount), 0.00), '$FM999,999,990.00') AS total,
    true AS is_total
FROM fees_by_amount fba

ORDER BY is_total, title, amount;
$$
where name = 'ClerkSummaryReportTableItems';