update config.sql_storage
set sql_query = $$
SELECT
    COALESCE(SUM(CASE WHEN p.payment_type = 'cash' THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS cash,
    COALESCE(SUM(CASE WHEN p.payment_type in ( 'link', 'CREDITCARD', 'card' ) THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS card,
    COALESCE(SUM(CASE WHEN p.payment_type = 'ach' THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS ach,
	COALESCE(SUM(CASE WHEN p.payment_type = 'personalCheck' THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS "personalCheck",
    COALESCE(SUM(CASE WHEN p.payment_type = 'certifiedCheck' THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS "certifiedCheck",
    COALESCE(SUM(CASE WHEN p.payment_type = 'moneyOrder' THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS "moneyOrder",
	COALESCE(SUM(fees.local_fees), 0.00)::NUMERIC(20,2)::TEXT AS "localTotal",
	COALESCE(SUM(fees.state_fees), 0.00)::NUMERIC(20,2)::TEXT AS "stateTotal",
	COALESCE(SUM(fees.stripe_fees), 0.00)::NUMERIC(20,2)::TEXT AS "stripeTotal",
	COALESCE(SUM(fees.total_fees), 0.00)::NUMERIC(20,2)::TEXT AS total
FROM calculation.order_ o
INNER JOIN calculation.order_item oi 
    ON o.order_id = oi.order_id
    AND o.status = 'ORDER_PAID'
    AND DATE(o.order_paid_date) >= DATE(:startDate)
    AND DATE(o.order_paid_date) <= DATE(:endDate)
    AND (oi.name LIKE 'Dog License%' OR oi.name LIKE 'Purebred%')
    AND oi.item_type_id = 'license'
INNER JOIN payment.payment p 
    ON p.order_id = o.order_id
LEFT JOIN LATERAL calculation.get_dog_license_fees_by_order_item_id(oi.order_item_id) AS fees
    ON true;
$$
where name = 'DogTransactionReportCombinedTotals';